using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Generates orbit location GameObjects for each celestial body in the scene
/// </summary>
public class OrbitLocationGenerator : MonoBehaviour
{
    [Header("Orbit Generation Settings")]
    [SerializeField] private bool generateOnStart = true;
    [SerializeField] private float orbitDistance = 5f; // Distance from parent body
    [SerializeField] private GameObject orbitLocationPrefab; // Optional prefab for orbit locations
    
    [Header("Generated Orbits")]
    [SerializeField] private List<GameObject> generatedOrbits = new List<GameObject>();
    
    private void Start()
    {
        if (generateOnStart)
        {
            GenerateOrbitLocations();
        }
    }
    
    /// <summary>
    /// Generate orbit locations for all celestial bodies
    /// </summary>
    [ContextMenu("Generate Orbit Locations")]
    public void GenerateOrbitLocations()
    {
        // Clear existing generated orbits
        ClearGeneratedOrbits();
        
        // Get WorldManager to find all celestial bodies
        WorldManager worldManager = WorldManager.Instance;
        if (worldManager == null)
        {
            Debug.LogError("WorldManager not found! Cannot generate orbit locations.");
            return;
        }
        
        List<GameObject> celestialBodies = worldManager.GetAllCelestialBodies();
        
        foreach (GameObject celestialBody in celestialBodies)
        {
            CreateOrbitLocation(celestialBody);
        }
        
        Debug.Log($"Generated {generatedOrbits.Count} orbit locations");

        // Notify WorldManager to refresh orbit locations
        if (worldManager != null)
        {
            worldManager.RefreshOrbitLocations();
        }
    }
    
    /// <summary>
    /// Create an orbit location for a specific celestial body
    /// </summary>
    private void CreateOrbitLocation(GameObject celestialBody)
    {
        if (celestialBody == null)
        {
            Debug.LogError("Cannot create orbit location for null celestial body!");
            return;
        }
        
        PlanetBody planetBody = celestialBody.GetComponent<PlanetBody>();
        if (planetBody == null)
        {
            Debug.LogWarning($"GameObject {celestialBody.name} doesn't have a PlanetBody component!");
            return;
        }
        
        // Check if orbit already exists
        string orbitName = $"Low {planetBody.Name} Orbit";
        GameObject existingOrbit = GameObject.Find(orbitName);
        if (existingOrbit != null)
        {
            Debug.Log($"Orbit location {orbitName} already exists, skipping creation");
            return;
        }
        
        // Create orbit location GameObject
        GameObject orbitLocation;
        if (orbitLocationPrefab != null)
        {
            orbitLocation = Instantiate(orbitLocationPrefab, transform);
        }
        else
        {
            orbitLocation = new GameObject();
            orbitLocation.transform.parent = transform;
        }
        
        orbitLocation.name = orbitName;
        
        // Position the orbit location near the celestial body
        Vector3 orbitPosition = celestialBody.transform.position + Vector3.up * orbitDistance;
        orbitLocation.transform.position = orbitPosition;
        
        // Add OrbitLocation component
        OrbitLocation orbitComponent = orbitLocation.GetComponent<OrbitLocation>();
        if (orbitComponent == null)
        {
            orbitComponent = orbitLocation.AddComponent<OrbitLocation>();
        }
        
        // Initialize the orbit location
        orbitComponent.Initialize(celestialBody);
        
        // Add to generated orbits list
        generatedOrbits.Add(orbitLocation);
        
        Debug.Log($"Created orbit location: {orbitName} at position {orbitPosition}");
    }
    
    /// <summary>
    /// Clear all generated orbit locations
    /// </summary>
    [ContextMenu("Clear Generated Orbits")]
    public void ClearGeneratedOrbits()
    {
        foreach (GameObject orbit in generatedOrbits)
        {
            if (orbit != null)
            {
                if (Application.isPlaying)
                {
                    Destroy(orbit);
                }
                else
                {
                    DestroyImmediate(orbit);
                }
            }
        }
        
        generatedOrbits.Clear();
        Debug.Log("Cleared all generated orbit locations");
    }
    
    /// <summary>
    /// Get all generated orbit locations
    /// </summary>
    public List<GameObject> GetGeneratedOrbits()
    {
        return new List<GameObject>(generatedOrbits);
    }
    
    /// <summary>
    /// Find orbit location for a specific celestial body
    /// </summary>
    public GameObject FindOrbitForCelestialBody(GameObject celestialBody)
    {
        if (celestialBody == null) return null;
        
        PlanetBody planetBody = celestialBody.GetComponent<PlanetBody>();
        if (planetBody == null) return null;
        
        string orbitName = $"Low {planetBody.Name} Orbit";
        
        foreach (GameObject orbit in generatedOrbits)
        {
            if (orbit != null && orbit.name == orbitName)
            {
                return orbit;
            }
        }
        
        return null;
    }
    
    private void OnValidate()
    {
        // Ensure orbit distance is positive
        if (orbitDistance < 0f)
        {
            orbitDistance = 0f;
        }
    }
}
