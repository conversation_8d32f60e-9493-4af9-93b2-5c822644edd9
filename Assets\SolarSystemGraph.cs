using System.Collections.Generic;
using UnityEngine;

public class SolarSystemGraph : MonoBehaviour
{
    // Singleton pattern
    public static SolarSystemGraph Instance;

    // CHANGE #4: Add a way to check if initialized
    public bool IsInitialized { get; private set; } = false;

    // Dictionary mapping GameObjects (celestial bodies and orbit locations) to their node IDs
    public Dictionary<GameObject, int> bodyToNodeId = new Dictionary<GameObject, int>();
    public Dictionary<int, GameObject> nodeIdToBody = new Dictionary<int, GameObject>();
    
    // Adjacency list representation of the graph
    public Dictionary<int, List<PathConnection>> adjacencyList = new Dictionary<int, List<PathConnection>>();
    
    private int nextNodeId = 0;

    // CHANGE #5: Add more debug logging to Awake
    private void Awake()
    {   
        if (Instance != null && Instance != this)
        {
            Debug.LogWarning("Multiple SolarSystemGraph instances detected! " +
                           "Existing on " + Instance.gameObject.name + 
                           ", new on " + gameObject.name);
            Destroy(gameObject);
            return;
        }
        
        Instance = this;
        DontDestroyOnLoad(gameObject);
    }

    // Structure to represent a connection between nodes
    public class PathConnection
    {
        public int DestinationNodeId;
        public float DeltaVCost;
        public GameObject PathVisual;
        
        public PathConnection(int destinationNodeId, float deltaVCost, GameObject pathVisual)
        {
            DestinationNodeId = destinationNodeId;
            DeltaVCost = deltaVCost;
            PathVisual = pathVisual;
        }
    }

    // Add a location (celestial body or orbit) to the graph
    public int AddNode(GameObject location)
    {
        if (location == null)
        {
            Debug.LogError("Trying to add null location to graph!");
            return -1;
        }

        // Check if already in graph
        if (bodyToNodeId.ContainsKey(location))
        {
            Debug.Log($"{location.name} already in graph with ID {bodyToNodeId[location]}");
            return bodyToNodeId[location];
        }

        int nodeId = nextNodeId++;
        bodyToNodeId[location] = nodeId;
        nodeIdToBody[nodeId] = location;
        adjacencyList[nodeId] = new List<PathConnection>();
        //Debug.Log($"Added {location.name} to graph with ID {nodeId}");

        IsInitialized = true;
        return nodeId;
    }

     // Add a ONE WAY path between two locations (celestial bodies or orbits)
    public void AddDirectionalPath(GameObject from, GameObject to, float deltaVCost, GameObject pathVisual)
    {
        // Make sure both locations exist in the graph
        if (!bodyToNodeId.ContainsKey(from))
            AddNode(from);
        if (!bodyToNodeId.ContainsKey(to))
            AddNode(to);

        int fromNodeId = bodyToNodeId[from];
        int toNodeId = bodyToNodeId[to];

        // Add one-way connection only
        adjacencyList[fromNodeId].Add(new PathConnection(toNodeId, deltaVCost, pathVisual));
    }

    // Get all outgoing connections from a location (celestial body or orbit)
    public List<PathConnection> GetConnections(GameObject location)
    {
        if (bodyToNodeId.ContainsKey(location))
        {
            int nodeId = bodyToNodeId[location];
            return adjacencyList[nodeId];
        }
        return new List<PathConnection>();
    }

    // Get location from node ID
    public GameObject GetBody(int nodeId)
    {
        if (nodeIdToBody.ContainsKey(nodeId))
            return nodeIdToBody[nodeId];
        return null;
    }

    // Get directional delta-v cost between two locations
    public float GetDeltaVCost(GameObject from, GameObject to)
    {
        if (!bodyToNodeId.ContainsKey(from) || !bodyToNodeId.ContainsKey(to))
            return float.MaxValue;

        int fromNodeId = bodyToNodeId[from];
        int toNodeId = bodyToNodeId[to];

        foreach (var connection in adjacencyList[fromNodeId])
        {
            if (connection.DestinationNodeId == toNodeId)
                return connection.DeltaVCost;
        }

        return float.MaxValue; // No direct connection in this direction
    }

    /// <summary>
    /// Add orbital transfer paths between a celestial body and its orbit
    /// </summary>
    public void AddOrbitTransferPaths(GameObject celestialBody, GameObject orbitLocation)
    {
        if (celestialBody == null || orbitLocation == null)
        {
            Debug.LogError("Cannot add orbit transfer paths for null objects!");
            return;
        }

        // Get delta-V costs from the celestial body
        PlanetBody planetBody = celestialBody.GetComponent<PlanetBody>();
        OrbitLocation orbit = orbitLocation.GetComponent<OrbitLocation>();

        if (planetBody == null || orbit == null)
        {
            Debug.LogError("Missing PlanetBody or OrbitLocation components for orbit transfer!");
            return;
        }

        // Add bidirectional paths between surface and orbit
        float surfaceToOrbitCost = planetBody.GetDeltaVToOrbit();
        float orbitToSurfaceCost = orbit.GetDeltaVToSurface();

        // Surface to orbit
        AddDirectionalPath(celestialBody, orbitLocation, surfaceToOrbitCost, null);

        // Orbit to surface
        AddDirectionalPath(orbitLocation, celestialBody, orbitToSurfaceCost, null);

        Debug.Log($"Added orbital transfer paths between {celestialBody.name} and {orbitLocation.name}");
    }

    /// <summary>
    /// Automatically add all orbit locations and their transfer paths
    /// </summary>
    public void InitializeOrbitLocations()
    {
        WorldManager worldManager = WorldManager.Instance;
        if (worldManager == null)
        {
            Debug.LogError("WorldManager not found! Cannot initialize orbit locations.");
            return;
        }

        // Get all orbit locations
        List<GameObject> orbitLocations = worldManager.GetAllOrbitLocations();

        foreach (GameObject orbit in orbitLocations)
        {
            // Add the orbit location to the graph
            AddNode(orbit);

            // Get the parent celestial body and add transfer paths
            OrbitLocation orbitComponent = orbit.GetComponent<OrbitLocation>();
            if (orbitComponent != null && orbitComponent.ParentCelestialBody != null)
            {
                AddOrbitTransferPaths(orbitComponent.ParentCelestialBody, orbit);
            }
        }

        Debug.Log($"Initialized {orbitLocations.Count} orbit locations in the navigation graph");
    }

    // CHANGE #6: Add debug method
    public void LogGraphContents()
    {
        Debug.Log($"Graph contains {bodyToNodeId.Count} locations:");
        /*
        foreach (var entry in bodyToNodeId)
        {
            Debug.Log($"- {entry.Key.name} (ID: {entry.Value})");
        }
        */
    }
}